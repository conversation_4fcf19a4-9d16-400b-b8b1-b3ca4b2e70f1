/* 主样式入口文件 */

/* 引入基础样式 */
@import url('./base/reset.css');

/* 引入组件样式 */
@import url('./components/base.css');
@import url('./components/modal.css');
@import url('./components/tags.css');
@import url('./components/markdown.css');

/* 引入工具类样式 */
@import url('./utilities/helpers.css');

/* 响应式优化 */
@media (max-width: 768px) {
    .modal .bg-white {
        margin: 1% auto;
        width: 95%;
        min-width: unset !important;
        max-width: 95% !important;
        max-height: 90vh;
    }

    /* 内容区域内边距优化 - 减少移动端的内边距 */
    .modal .p-8 {
        padding: 1rem;
    }

    /* 头部和底部区域内边距优化 */
    .modal .px-8 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* 确保编辑器在移动端有合适的高度 */
    #editModal .min-h-250 {
        min-height: 180px;
    }

    /* 移动端工具栏优化 */
    #editModal .markdown-toolbar {
        padding: 6px 8px;
        gap: 2px;
    }

    #editModal .toolbar-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    /* 标签区域内边距优化 */
    #editModal .group {
        padding: 8px 12px !important;
        min-height: 48px !important;
    }

    /* 标签容器间距优化 */
    #editModal .space-y-6 > * + * {
        margin-top: 1rem;
    }

    /* 标签输入框内边距优化 */
    #editModal .group input {
        padding: 4px 6px !important;
    }

    /* 标签标题间距优化 */
    #editModal .mb-1\.5 {
        margin-bottom: 0.5rem !important;
    }

    /* 标签容器内部间距优化 */
    #editModal .gap-2 {
        gap: 0.375rem !important;
    }

    /* 统一模态框关闭按钮样式 - 移动端 */
    .modal .close-button,
    .modal button[data-dismiss="modal"]:not(.custom-modal-button) {
        width: 44px !important;
        height: 44px !important;
        font-size: 24px !important;
        min-width: 44px !important;
        min-height: 44px !important;
    }
}

@media (max-width: 480px) {
    .modal .bg-white {
        margin: 0.5% auto;
        width: 98%;
        min-width: unset !important;
        max-width: 98% !important;
        max-height: 95vh;
    }

    /* 小屏幕设备内容区域内边距进一步减少 */
    .modal .p-8 {
        padding: 0.75rem;
    }

    /* 头部和底部区域内边距进一步减少 */
    .modal .px-8 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* 小屏幕设备进一步优化 */
    #editModal .min-h-250 {
        min-height: 150px;
    }

    /* 标签输入区域进一步优化 */
    #editModal .group {
        padding: 6px 10px !important;
        min-height: 44px !important;
    }

    /* 标签容器间距进一步优化 */
    #editModal .space-y-6 > * + * {
        margin-top: 0.75rem;
    }

    /* 标签输入框内边距进一步优化 */
    #editModal .group input {
        padding: 3px 4px !important;
        min-width: 100px !important;
    }

    /* 标签标题间距进一步优化 */
    #editModal .mb-1\.5 {
        margin-bottom: 0.375rem !important;
    }

    /* 标签容器内部间距进一步优化 */
    #editModal .gap-2 {
        gap: 0.25rem !important;
    }

    /* 标签元素间距优化 */
    #editModal .gap-4 {
        gap: 0.5rem !important;
    }

    /* 编辑器内边距优化 */
    #editModal textarea {
        padding: 1rem !important;
    }

    /* 保存按钮优化 */
    #editModal button[type="submit"] {
        padding: 12px 20px;
        font-size: 16px;
        min-height: 44px;
    }

    /* 关闭按钮优化 */
    #editModal button[data-dismiss="modal"]:not(.custom-modal-button) {
        width: 44px;
        height: 44px;
        font-size: 24px;
    }

    /* Now按钮优化 */
    #editModal #setCurrentTimeBtn {
        min-height: 36px;
        padding: 8px 12px;
        font-size: 14px;
    }

    /* 时间输入框优化 */
    #editModal #editPostTime {
        min-height: 36px;
        padding: 8px 12px;
        font-size: 14px;
    }

    /* 头部区域内边距优化 */
    #editModal .py-6 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
}

/* 触摸设备特定优化 */
@media (hover: none) and (pointer: coarse) {
    /* 移除hover效果，避免在触摸设备上的粘滞问题 */
    #editModal .toolbar-btn:hover {
        background: transparent;
        color: #64748b;
    }

    /* 增加触摸目标大小 */
    #editModal .toolbar-btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* 优化按钮间距 */
    #editModal .markdown-toolbar {
        gap: 8px;
    }
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
    /* 平滑滚动 */
    #editModal .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* 防止内容被虚拟键盘遮挡 */
    #editModal {
        padding-bottom: env(keyboard-inset-height, 0px);
    }
}

/* 确保模态框内容区域正确滚动 */
#editModal .flex-1.overflow-y-auto {
    /* 确保滚动容器有明确的高度 */
    min-height: 0;
    /* 优化滚动性能 */
    will-change: scroll-position;
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Webkit浏览器滚动条样式 */
#editModal .flex-1.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#editModal .flex-1.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#editModal .flex-1.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#editModal .flex-1.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 防止缩放和改善触摸体验 */
@media (max-width: 768px) {
    #editModal input,
    #editModal textarea {
        font-size: 16px; /* 防止iOS自动缩放 */
        -webkit-appearance: none;
        border-radius: 8px;
    }

    /* 改善文本选择体验 */
    #editModal textarea {
        -webkit-user-select: text;
        user-select: text;
    }
}

/* 极低窗口高度优化 (高度小于500px) */
@media (max-height: 500px) {
    .modal .bg-white {
        margin: 0.5% auto;
        max-height: 98vh;
    }

    /* 进一步减少内边距 */
    .modal .p-8 {
        padding: 0.5rem !important;
    }

    .modal .px-8 {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }

    .modal .py-6 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    /* 减少编辑器最小高度 */
    #editModal .min-h-250,
    #editModal .md\:min-h-300 {
        min-height: 120px !important;
    }

    /* 压缩标签区域 */
    #editModal .space-y-6 > * + * {
        margin-top: 0.5rem !important;
    }

    #editModal .group {
        padding: 4px 8px !important;
        min-height: 40px !important;
    }

    /* 压缩工具栏 */
    #editModal .markdown-toolbar {
        padding: 4px 6px !important;
        gap: 1px !important;
    }

    #editModal .toolbar-btn {
        width: 24px !important;
        height: 24px !important;
        font-size: 10px !important;
    }
}

/* 超低窗口高度优化 (高度小于400px) */
@media (max-height: 400px) {
    .modal .bg-white {
        margin: 0 auto;
        max-height: 99vh;
    }

    /* 最小化所有内边距 */
    .modal .p-8 {
        padding: 0.25rem !important;
    }

    .modal .px-8 {
        padding-left: 0.25rem !important;
        padding-right: 0.25rem !important;
    }

    .modal .py-6 {
        padding-top: 0.25rem !important;
        padding-bottom: 0.25rem !important;
    }

    /* 最小化编辑器高度 */
    #editModal .min-h-250,
    #editModal .md\:min-h-300 {
        min-height: 60px !important;
        max-height: 80px !important;
    }

    /* 最小化间距 */
    #editModal .space-y-6 > * + * {
        margin-top: 0.25rem !important;
    }

    #editModal .group {
        padding: 2px 4px !important;
        min-height: 32px !important;
    }

    /* 隐藏工具栏以节省空间 */
    #editModal .markdown-toolbar {
        display: none !important;
    }

    /* 调整按钮大小 */
    #editModal button[type="submit"] {
        padding: 8px 16px !important;
        font-size: 14px !important;
        min-height: 36px !important;
    }
}

/* 去掉添加标签输入框的边框 */
#tagInput,
#editTagInput {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}