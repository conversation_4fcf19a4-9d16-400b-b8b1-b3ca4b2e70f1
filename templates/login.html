<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入第三方库样式 -->
    <link href="/static/css/vendors/tailwind.min.css" rel="stylesheet">
    <!-- 引入主样式文件 -->
    <link rel="stylesheet" href="/static/css/main.css">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">登录</h1>
        <form method="POST" action="/login" class="space-y-4">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                <input type="text" id="username" name="username" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                <input type="password" id="password" name="password" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <button type="submit"
                class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition duration-300">登录</button>
        </form>
    </div>

    <!-- 登录失败弹窗 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden" id="errorModal">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div class="flex justify-between items-center border-b border-gray-200 px-6 py-4">
                <h5 class="text-lg font-medium text-gray-800">登录失败</h5>
                <button class="close-button text-gray-500 hover:text-gray-700 text-2xl leading-none">&times;</button>
            </div>
            <div class="px-6 py-4">
                <p class="text-red-500 font-medium">用户名或密码错误，请重试！</p>
            </div>
        </div>
    </div>

    <script>
        const form = document.querySelector('form');
        const errorModal = document.getElementById('errorModal');
        const closeButton = errorModal.querySelector('.close-button');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(form);

            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    window.location.href = '/';
                } else {
                    errorModal.classList.remove('hidden');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                errorModal.classList.remove('hidden');
            }
        });

        closeButton.addEventListener('click', () => {
            errorModal.classList.add('hidden');
        });

        // 点击模态框外部关闭
        errorModal.addEventListener('click', (e) => {
            if (e.target === errorModal) {
                errorModal.classList.add('hidden');
            }
        });
    </script>
</body>
</html>