<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的笔记</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入第三方库样式 -->
    <link href="/static/css/vendors/tailwind.min.css" rel="stylesheet">
    <!-- 引入主样式文件 -->
    <link rel="stylesheet" href="/static/css/main.css">
</head>

<body>
    <!-- 导航栏 -->
    {% set active_page = 'index' %}
    {% include '_navbar.html' %}

    <!-- 主容器 - 使用 Tailwind CSS -->
    <div class="container max-w-4xl mx-auto px-8 pt-20 md:px-4 md:pt-16">

        {% if current_tag %}
        <div class="bg-gradient-to-br from-indigo-50 to-purple-50 px-6 py-4 rounded-xl mb-6 font-medium text-gray-800 border border-indigo-200">
            当前筛选：<span class="text-indigo-600 font-semibold">{% if current_tag == 'no_tags' %}无标签{% else %}{{ current_tag }}{% endif %}</span>
            <a href="{{ url_for('index') }}" class="text-gray-600 no-underline ml-2 font-medium hover:text-indigo-600">[显示全部]</a>
        </div>
        {% endif %}

        <!-- 发布笔记对话框 -->
        {% include '_post_modal.html' %}


        <!-- 搜索结果为空的提示 -->
        {% if search_empty %}
        <div class="empty-state flex flex-col items-center justify-center py-16 text-center">
            <div class="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
                <svg class="w-12 h-12 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">未找到相关笔记</h3>
            <p class="text-gray-600 mb-6 max-w-md">
                抱歉，没有找到与 "<span class="text-indigo-600 font-medium">{{ search_query }}</span>" 相关的笔记内容。
            </p>
            <a href="{{ url_for('index') }}" class="px-6 py-2.5 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium rounded-xl transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5">
                查看全部笔记
            </a>
        </div>
        {% else %}
        <!-- 笔记列表 -->
        <div class="posts-list space-y-8 md:space-y-6">
            {% for post in posts %}
            <div class="card bg-white rounded-2xl p-8 shadow-sm border border-gray-200 relative overflow-hidden md:p-6" data-post-id="{{ post.id }}">
                <div class="card-header mb-0">
                    <div class="header-row flex items-center justify-between mb-4">
                        <div class="post-time text-xs text-gray-500 font-medium">{{ post.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        <div class="btn-group flex gap-2">
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="{{ post.id }}">编辑</button>
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="{{ post.id }}">删除</button>
                        </div>
                    </div>
                    <div class="post-content leading-relaxed text-gray-800 mb-6 break-words markdown-content" id="content-{{ post.id }}">{{ post.rendered_content|safe }}</div>
                    <!-- 标签容器 -->
                    {% if post.tags %}
                    <div class="tags-container mt-4">
                        {% for tag in post.tags %}
                        <a href="{{ url_for('index', tag=tag) }}"
                            class="note-tag {% if current_tag == tag %}note-tag--active{% else %}note-tag--normal{% endif %}">
                            <!-- 标签文本 -->
                            <span class="tag-text">{{ tag }}</span>

                            <!-- 激活状态指示器 -->
                            {% if current_tag == tag %}
                            <span class="tag-indicator"></span>
                            {% endif %}
                        </a>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 分页导航 -->
        {% if pagination.pages > 1 %}
        <nav class="pagination flex justify-center items-center gap-2 mt-12 mb-8">
            {% set url_params = {} %}
            {% if request.args.get('q') %}
                {% set _ = url_params.update({'q': request.args.get('q')}) %}
            {% endif %}
            {% if current_tag %}
                {% set _ = url_params.update({'tag': current_tag}) %}
            {% endif %}

            <a href="{{ url_for('index', page=pagination.prev_num, **url_params) if pagination.has_prev else '#' }}"
                class="flex items-center justify-center w-10 h-10 rounded-xl no-underline font-medium transition-all duration-300 {% if not pagination.has_prev %}text-gray-400 cursor-not-allowed opacity-50{% else %}text-gray-600 border border-gray-200 bg-white hover:bg-indigo-600 hover:text-white hover:shadow-sm{% endif %}">&laquo;</a>
            {% for page in pagination.iter_pages() %}
            {% if page %}
            <a href="{{ url_for('index', page=page, **url_params) }}" class="flex items-center justify-center w-10 h-10 rounded-xl no-underline font-medium transition-all duration-300 {% if page == pagination.page %}bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0{% else %}text-gray-600 border border-gray-200 bg-white hover:bg-indigo-600 hover:text-white hover:shadow-sm{% endif %}">{{ page }}</a>
            {% else %}
            <span class="flex items-center justify-center w-10 h-10 rounded-xl text-gray-400 cursor-not-allowed opacity-50">...</span>
            {% endif %}
            {% endfor %}
            <a href="{{ url_for('index', page=pagination.next_num, **url_params) if pagination.has_next else '#' }}"
                class="flex items-center justify-center w-10 h-10 rounded-xl no-underline font-medium transition-all duration-300 {% if not pagination.has_next %}text-gray-400 cursor-not-allowed opacity-50{% else %}text-gray-600 border border-gray-200 bg-white hover:bg-indigo-600 hover:text-white hover:shadow-sm{% endif %}">&raquo;</a>
        </nav>
        {% endif %}
    </div> <!-- 主容器结束 -->

    <!-- 悬浮发布按钮 -->
    <button id="postButton"
            class="fixed bottom-8 right-8 w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/30 border-0 cursor-pointer flex items-center justify-center transition-all duration-300 ease-out z-40 hover:shadow-xl hover:shadow-indigo-500/40 hover:scale-110 active:scale-95 md:bottom-6 md:right-6 md:w-12 md:h-12">
        <svg class="w-6 h-6 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
    </button>

    <!-- 删除确认对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="deleteConfirmModal">
        <div class="bg-white my-[10%] mx-auto rounded-2xl w-[95%] max-w-lg shadow-2xl animate-slide-in overflow-hidden">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800 m-0">确认删除</h5>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body p-8">
                <p class="text-gray-700 text-base">确定要删除这条微博吗？</p>
            </div>
            <div class="modal-footer px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
                <button type="button" class="px-4 py-2 text-sm font-semibold bg-indigo-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-indigo-700" data-dismiss="modal">取消</button>
                <form id="deletePostForm" method="POST" class="inline">
                    <button type="submit" class="btn-danger px-4 py-2 text-sm font-semibold bg-red-500 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-red-600">删除</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}',
            updatePost: '{{ url_for("update_post", post_id=0) }}'
        };
        const searchQuery = "{{ search_query or '' }}";
        
        // 检查URL参数，如果有openPost=1则自动打开发布模态框
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('openPost') === '1') {
                // 延迟一点时间确保页面完全加载和main.js初始化完成
                setTimeout(function() {
                    const postButton = document.getElementById('postButton');
                    if (postButton) {
                        postButton.click();
                    }
                    // 清理URL参数，避免刷新页面时重复打开
                    const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
                    window.history.replaceState({path: newUrl}, '', newUrl);
                }, 100);
            }
        });
    </script>
    <script src="/static/js/search.js"></script>
    <script src="/static/js/tags.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/navbar.js"></script>
</body>

</html>
