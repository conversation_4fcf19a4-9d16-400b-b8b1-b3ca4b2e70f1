<!-- 通用笔记模态框 (新建/编辑) -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[320px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl border border-gray-300 animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
        <!-- 模态框头部 -->
        <div class="flex justify-between items-center px-6 py-3 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 flex-shrink-0">
            <button type="button" class="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white font-semibold rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 hover:shadow-gray-500/25 text-sm shadow-sm custom-modal-button" data-dismiss="modal">
                取消
            </button>
            <h5 class="text-lg font-semibold text-gray-800 m-0 absolute left-1/2 transform -translate-x-1/2 px-4" id="modalTitle">发布笔记</h5>
            <button type="submit" form="postForm" id="submitButton" class="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 hover:shadow-indigo-500/25 text-sm shadow-sm custom-modal-button">
                发布
            </button>
        </div>
        
        <!-- 模态框内容 -->
        <div class="px-6 pt-0 pb-6 flex-1 overflow-y-auto">
            <form id="postForm">
                <!-- 隐藏的笔记ID字段 -->
                <input type="hidden" id="postIdField" name="post_id">

                <!-- Markdown 编辑器 -->
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden focus-within:border-indigo-500 focus-within:shadow-sm focus-within:shadow-indigo-100 transition-all duration-300">
                    <div class="editor-container">
                        <textarea name="content" id="modalMarkdownEditor" placeholder="记录你的想法和灵感..." required
                                  class="w-full min-h-250 md:min-h-300 p-5 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                    </div>
                </div>
                
                <!-- 发布时间设置 (仅编辑时显示) -->
                <div id="editTimeWrapper" class="hidden bg-gray-50 rounded-lg px-3 py-1 border border-gray-200 mt-4">
                    <div class="flex items-center justify-end">
                        <div class="flex items-center gap-2">
                            <input type="datetime-local" id="postTimeInput" name="created_at" 
                                   class="px-2 py-1 text-sm border border-gray-300 rounded-md transition-all duration-200 bg-white text-gray-600 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-100">
                            <button type="button" id="setCurrentTimeBtn" class="px-2.5 py-1 text-sm font-medium bg-indigo-500 text-white rounded-md cursor-pointer transition-all duration-200 hover:bg-indigo-600">
                                现在
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 标签编辑区域 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <div class="tags-input-wrapper tags-component">
                            <div class="tags-container flex flex-wrap gap-2" id="modalTagsContainer"></div>
                            <input type="text" id="modalTagInput" placeholder="添加标签..."
                                   class="flex-1 min-w-0 border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 focus:border-0 focus:outline-0 focus:ring-0" />
                        </div>
                        <input type="hidden" name="tags" id="modalTagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>

        
    </div>
</div>


<style>
/* 模态框通用样式 */
.modal { display: none; }
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 动画效果 */
@keyframes modalFadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes modalSlideIn { from { transform: translateY(-50px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
.animate-fade-in { animation: modalFadeIn 0.3s ease; }
.animate-slide-in { animation: modalSlideIn 0.3s ease; }

/* 编辑器和标签输入区域样式 */
#postModal .editor-container { position: relative; border: 0; border-radius: 0 0 12px 12px; overflow: hidden; }
#postModal .tags-input-wrapper {
    display: flex; flex-wrap: wrap; align-items: center; gap: 0.5rem;
    padding: 0.75rem; border: 0; border-radius: 12px;
    background: white; transition: all 0.3s ease;
}
#postModal .tags-input-wrapper:focus-within {
    box-shadow: none;
    outline: none;
    border: 0;
}

/* 编辑器聚焦状态 */
#postModal .border-2.border-gray-200:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 移动端优化 */
@media (max-width: 768px) {
    #postModal textarea { padding: 1rem !important; }
    #postModal .p-6 { padding: 1rem !important; }
    #postModal .px-6 { padding-left: 1rem !important; padding-right: 1rem !important; }
    #postModal .py-5 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
    #postModal .min-w-\[600px\] { min-width: unset !important; }

    /* 日期时间控件移动端优化 */
    #editTimeWrapper {
        overflow: hidden; /* 防止内容溢出 */
    }

    #editTimeWrapper .flex.items-center.justify-end {
        justify-content: flex-start !important; /* 改为左对齐，消除左侧空白 */
        overflow: hidden; /* 防止溢出 */
    }

    #editTimeWrapper .flex.items-center.gap-2 {
        flex-wrap: nowrap; /* 不换行，保持水平布局 */
        gap: 0.5rem; /* 减少间距 */
        width: 100%; /* 占满宽度 */
        min-width: 0; /* 允许收缩 */
        overflow: hidden; /* 防止溢出 */
    }

    #postTimeInput {
        flex: 1; /* 让日期输入框占据剩余空间 */
        min-width: 0; /* 允许收缩 */
        max-width: 100%; /* 限制最大宽度 */
        font-size: 14px !important; /* 调整字体大小 */
        width: auto !important; /* 覆盖浏览器默认宽度 */
    }

    #setCurrentTimeBtn {
        flex-shrink: 0; /* 按钮不收缩 */
        font-size: 14px !important; /* 调整字体大小 */
        padding: 0.5rem 0.75rem !important; /* 调整内边距 */
        white-space: nowrap; /* 防止文字换行 */
    }
}

@media (max-width: 480px) {
    #postModal textarea { padding: 0.75rem !important; }
    #postModal .p-6 { padding: 0.75rem !important; }
    #postModal .px-6 { padding-left: 0.75rem !important; padding-right: 0.75rem !important; }
    #postModal .py-5 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }

    /* 小屏幕设备进一步优化日期时间控件 */
    #editTimeWrapper {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
        overflow: hidden; /* 防止溢出 */
    }

    #editTimeWrapper .flex.items-center.gap-2 {
        flex-direction: column; /* 垂直排列 */
        align-items: stretch; /* 拉伸对齐 */
        gap: 0.5rem;
        overflow: hidden; /* 防止溢出 */
    }

    #postTimeInput {
        width: 100%; /* 全宽 */
        max-width: 100%; /* 限制最大宽度 */
        text-align: center; /* 居中对齐 */
        box-sizing: border-box; /* 包含边框和内边距 */
    }

    #setCurrentTimeBtn {
        width: 100%; /* 全宽按钮 */
        max-width: 100%; /* 限制最大宽度 */
        justify-content: center; /* 居中对齐 */
        box-sizing: border-box; /* 包含边框和内边距 */
    }
}

/* 针对所有屏幕尺寸的日期时间输入框优化 */
#postTimeInput[type="datetime-local"] {
    /* 覆盖浏览器默认样式 */
    -webkit-appearance: none;
    -moz-appearance: textfield;
    appearance: none;

    /* 确保输入框不会超出容器 */
    max-width: 100%;
    box-sizing: border-box;

    /* 处理内容溢出 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 针对 WebKit 浏览器的日期时间输入框 */
#postTimeInput[type="datetime-local"]::-webkit-datetime-edit {
    padding: 0;
    margin: 0;
}

#postTimeInput[type="datetime-local"]::-webkit-calendar-picker-indicator {
    margin-left: 4px;
    opacity: 0.6;
    cursor: pointer;
}

/* 中等屏幕尺寸的额外优化 */
@media (max-width: 1024px) and (min-width: 481px) {
    #editTimeWrapper .flex.items-center.gap-2 {
        flex-wrap: wrap; /* 在中等屏幕允许换行 */
    }

    #postTimeInput {
        min-width: 200px; /* 设置最小宽度 */
        flex-basis: auto; /* 自动基础宽度 */
    }

    #setCurrentTimeBtn {
        min-width: 60px; /* 按钮最小宽度 */
    }
}
</style>
