<!-- 通用笔记模态框 (新建/编辑) -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[320px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl border border-gray-300 animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
        <!-- 模态框头部 -->
        <div class="flex justify-between items-center px-6 py-3 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 flex-shrink-0">
            <button type="button" class="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white font-semibold rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 hover:shadow-gray-500/25 text-sm shadow-sm custom-modal-button" data-dismiss="modal">
                取消
            </button>
            <h5 class="text-lg font-semibold text-gray-800 m-0 absolute left-1/2 transform -translate-x-1/2 px-4" id="modalTitle">发布笔记</h5>
            <button type="submit" form="postForm" id="submitButton" class="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 hover:shadow-indigo-500/25 text-sm shadow-sm custom-modal-button">
                发布
            </button>
        </div>
        
        <!-- 模态框内容 -->
        <div class="px-6 pt-0 pb-6 flex-1 overflow-y-auto">
            <form id="postForm">
                <!-- 隐藏的笔记ID字段 -->
                <input type="hidden" id="postIdField" name="post_id">

                <!-- Markdown 编辑器 -->
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden focus-within:border-indigo-500 focus-within:shadow-sm focus-within:shadow-indigo-100 transition-all duration-300">
                    <div class="editor-container">
                        <textarea name="content" id="modalMarkdownEditor" placeholder="记录你的想法和灵感..." required
                                  class="w-full min-h-250 md:min-h-300 p-5 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                    </div>
                </div>
                
                <!-- 发布时间设置 (仅编辑时显示) -->
                <div id="editTimeWrapper" class="hidden bg-gray-50 rounded-lg px-3 py-1 border border-gray-200 mt-4">
                    <div class="flex items-center justify-end">
                        <div class="flex items-center gap-2">
                            <input type="datetime-local" id="postTimeInput" name="created_at" 
                                   class="px-2 py-1 text-sm border border-gray-300 rounded-md transition-all duration-200 bg-white text-gray-600 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-100">
                            <button type="button" id="setCurrentTimeBtn" class="px-2.5 py-1 text-sm font-medium bg-indigo-500 text-white rounded-md cursor-pointer transition-all duration-200 hover:bg-indigo-600">
                                现在
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 标签编辑区域 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <div class="tags-input-wrapper tags-component">
                            <div class="tags-container flex flex-wrap gap-2" id="modalTagsContainer"></div>
                            <input type="text" id="modalTagInput" placeholder="添加标签..."
                                   class="flex-1 min-w-0 border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 focus:border-0 focus:outline-0 focus:ring-0" />
                        </div>
                        <input type="hidden" name="tags" id="modalTagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>

        
    </div>
</div>


<style>
/* 模态框通用样式 */
.modal { display: none; }
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 动画效果 */
@keyframes modalFadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes modalSlideIn { from { transform: translateY(-50px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
.animate-fade-in { animation: modalFadeIn 0.3s ease; }
.animate-slide-in { animation: modalSlideIn 0.3s ease; }

/* 编辑器和标签输入区域样式 */
#postModal .editor-container { position: relative; border: 0; border-radius: 0 0 12px 12px; overflow: hidden; }
#postModal .tags-input-wrapper {
    display: flex; flex-wrap: wrap; align-items: center; gap: 0.5rem;
    padding: 0.75rem; border: 0; border-radius: 12px;
    background: white; transition: all 0.3s ease;
}
#postModal .tags-input-wrapper:focus-within {
    box-shadow: none;
    outline: none;
    border: 0;
}

/* 编辑器聚焦状态 */
#postModal .border-2.border-gray-200:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 移动端优化 */
@media (max-width: 768px) { 
    #postModal textarea { padding: 1rem !important; }
    #postModal .p-6 { padding: 1rem !important; }
    #postModal .px-6 { padding-left: 1rem !important; padding-right: 1rem !important; }
    #postModal .py-5 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
    #postModal .min-w-\[600px\] { min-width: unset !important; }
}
@media (max-width: 480px) { 
    #postModal textarea { padding: 0.75rem !important; }
    #postModal .p-6 { padding: 0.75rem !important; }
    #postModal .px-6 { padding-left: 0.75rem !important; padding-right: 0.75rem !important; }
    #postModal .py-5 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }
}
</style>
